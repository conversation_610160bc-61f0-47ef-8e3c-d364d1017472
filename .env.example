# KULLM-Pro Environment Variables
# Copy this file to .env and fill in your actual values

# OpenAI API Configuration
# Required for code switching functionality
OPENAI_API_KEY=your_openai_api_key_here

# Weights & Biases Configuration
# Optional - for experiment tracking and logging
WANDB_API_KEY=your_wandb_api_key_here
WANDB_ENTITY=your_wandb_entity_name
WANDB_PROJECT=kullm-pro

# Hugging Face Configuration
# Optional - for accessing private models or datasets
HF_TOKEN=your_hugging_face_token_here
HUGGINGFACE_HUB_TOKEN=your_hugging_face_token_here

# CUDA Configuration
# Optional - for GPU optimization
CUDA_VISIBLE_DEVICES=0,1,2,3
PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512

# Training Configuration Overrides
# Optional - override config.yaml values via environment
# KULLM_MODEL_NAME=Qwen/Qwen2.5-7B-Instruct
# KULLM_MAX_LENGTH=2048
# KULLM_BATCH_SIZE=2
# KULLM_LEARNING_RATE=2e-4
# KULLM_EPOCHS=3

# Logging Configuration
# Optional - configure logging behavior
# KULLM_LOG_LEVEL=INFO
# KULLM_LOG_FILE=./logs/kullm_pro.log

# Data Paths
# Optional - default data and output directories
# KULLM_DATA_DIR=./data
# KULLM_OUTPUT_DIR=./outputs

# Advanced Configuration
# Optional - for advanced users
# TOKENIZERS_PARALLELISM=false
# OMP_NUM_THREADS=1
# MKL_NUM_THREADS=1
