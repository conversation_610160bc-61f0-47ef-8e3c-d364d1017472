import os
import time
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer, pipeline
from typing import Optional
import fire

try:
    from peft import PeftModel
    PEFT_AVAILABLE = True
except ImportError:
    PEFT_AVAILABLE = False
    print("Warning: peft not available. LoRA models cannot be loaded.")


def load_model_and_tokenizer(base_model: str, lora_path: Optional[str] = None):
    """Load model and tokenizer, with optional LoRA adapter"""
    print(f"Loading base model: {base_model}")

    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(base_model, trust_remote_code=True)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
        tokenizer.pad_token_id = tokenizer.eos_token_id

    # Load base model
    model = AutoModelForCausalLM.from_pretrained(
        base_model,
        torch_dtype=torch.bfloat16,
        device_map="auto",
        trust_remote_code=True
    )

    # Load LoRA adapter if specified
    if lora_path is not None:
        if not PEFT_AVAILABLE:
            raise ImportError("peft library is required for LoRA models. Install with: pip install peft")
        print(f"Loading LoRA adapter from: {lora_path}")
        model = PeftModel.from_pretrained(model, lora_path)
        print("LoRA adapter loaded successfully")

    print("Model loaded successfully")
    return model, tokenizer


def get_pipeline(model, tokenizer):
    """Create text generation pipeline"""
    generator = pipeline(
        "text-generation",
        model=model,
        tokenizer=tokenizer,
        pad_token_id=tokenizer.pad_token_id,
        eos_token_id=[tokenizer.eos_token_id, 128001],
    )
    return generator


def main(
    base_model: str,
    lora_path: Optional[str] = None,
    sys_prompt: Optional[str] = None,
    max_new_tokens: int = 2048,
    **kwargs
):
    """
    Interactive chat with a language model.

    Args:
        base_model: Path or name of the base model (e.g., 'Qwen/Qwen2.5-7B-Instruct')
        lora_path: Optional path to LoRA adapter directory
        sys_prompt: Optional system prompt to use
        max_new_tokens: Maximum number of new tokens to generate
        **kwargs: Additional generation parameters

    Examples:
        # Use base model only
        python chat.py --base_model="Qwen/Qwen2.5-7B-Instruct"

        # Use base model with LoRA adapter
        python chat.py --base_model="Qwen/Qwen2.5-7B-Instruct" --lora_path="./outputs/my_model"

        # With system prompt
        python chat.py --base_model="Qwen/Qwen2.5-7B-Instruct" --sys_prompt="You are a helpful assistant."
    """
    print(f"Base model: {base_model}")
    if lora_path:
        print(f"LoRA adapter: {lora_path}")

    # Load model and tokenizer
    model, tokenizer = load_model_and_tokenizer(base_model, lora_path)

    print("Tokenizer info:")
    print(f"  bos_token: {tokenizer.bos_token} (id: {tokenizer.bos_token_id})")
    print(f"  eos_token: {tokenizer.eos_token} (id: {tokenizer.eos_token_id})")
    print(f"  pad_token: {tokenizer.pad_token} (id: {tokenizer.pad_token_id})")

    # Create pipeline
    pipe = get_pipeline(model, tokenizer)
    # Initialize conversation
    messages = []
    if sys_prompt is not None:
        messages.append({"role": "system", "content": sys_prompt})
        print(f"System prompt set: {sys_prompt[:100]}{'...' if len(sys_prompt) > 100 else ''}")

    print("\n" + "="*60)
    print("🤖 Interactive Chat Started!")
    print("Commands: 'clear' to reset conversation, 'exit' to quit")
    print("="*60 + "\n")

    while True:
        try:
            input_ = input("\033[94m💬 Enter instruction: \033[0m")

            if input_.strip() == "clear":
                messages = []
                if sys_prompt:
                    messages.append({"role": "system", "content": sys_prompt})
                os.system("clear")
                print("🔄 Conversation cleared!")
                continue
            elif input_.strip() == "exit":
                print("👋 Goodbye!")
                break
            elif input_.strip() == "":
                continue

            messages.append({"role": "user", "content": input_})
            os.system("clear")

            # Display conversation history
            for m in messages[:-1]:
                role_color = "\033[92m" if m["role"] == "system" else "\033[93m" if m["role"] == "user" else "\033[95m"
                print(f"{role_color}{m['role'].title()}: \033[0m{m['content']}")

            print(f"\033[93mUser: \033[0m{input_}")
            print(f"\033[95mAssistant: \033[0m", end="", flush=True)

            start = time.time()
            text = tokenizer.apply_chat_template(
                messages, add_generation_prompt=True, tokenize=False
            )

            result = pipe(
                text,
                return_full_text=False,
                clean_up_tokenization_spaces=True,
                max_new_tokens=max_new_tokens,
                do_sample=kwargs.get("do_sample", False),
                **kwargs,
            )[0]["generated_text"]

            messages.append({"role": "assistant", "content": result})
            print(result)
            print(f"\n⏱️  Response time: {time.time() - start:.2f}s")
            print("-" * 60)

        except KeyboardInterrupt:
            print("\n\n👋 Chat interrupted. Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")
            print("Please try again or type 'exit' to quit.")


if __name__ == "__main__":
    fire.Fire(main)
