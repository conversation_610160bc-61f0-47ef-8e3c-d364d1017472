# KULLM-Pro Configuration
# Production-ready configuration for code switching and fine-tuning

# Model Configuration
model:
  name: "Qwen/Qwen2.5-7B-Instruct"  # Base model for fine-tuning
  max_length: 2048                   # Maximum sequence length
  torch_dtype: "bfloat16"           # Model precision
  device_map: "auto"                # Device mapping strategy
  attn_implementation: "eager"      # Attention implementation

# Training Configuration
training:
  # Basic Training Parameters
  epochs: 3
  batch_size: 2
  learning_rate: 0.0002
  gradient_accumulation_steps: 8
  weight_decay: 0.01
  warmup_ratio: 0.1
  lr_scheduler_type: "cosine"

  # Evaluation and Saving
  eval_steps: 100
  save_steps: 500
  logging_steps: 10
  save_total_limit: 3
  load_best_model_at_end: true
  metric_for_best_model: "eval_loss"

  # Performance Optimizations
  bf16: true
  fp16: false
  gradient_checkpointing: false
  dataloader_num_workers: 0
  optim: "adamw_torch"

  # Advanced Training Options
  remove_unused_columns: false
  ddp_find_unused_parameters: false
  eval_strategy: "steps"
  save_strategy: "steps"
  greater_is_better: false

# LoRA Configuration
lora:
  r: 16                            # LoRA rank
  alpha: 32                        # LoRA alpha parameter
  dropout: 0.1                     # LoRA dropout
  bias: "none"                     # Bias configuration
  task_type: "CAUSAL_LM"          # Task type
  target_modules:                  # Target modules for LoRA
    - "q_proj"
    - "k_proj"
    - "v_proj"
    - "o_proj"
    - "gate_proj"
    - "up_proj"
    - "down_proj"

# Experiment Tracking (Weights & Biases)
wandb:
  project: "kullm-pro"            # WandB project name
  enabled: true                   # Enable/disable WandB logging
  entity: null                    # WandB entity (optional)
  tags: []                        # Additional tags for runs

# Data Configuration
data:
  train_split_ratio: 0.9          # Train/validation split ratio
  val_split_ratio: 0.1            # Validation split ratio (if no separate val file)

# OpenAI Configuration (for code switching)
openai:
  model: "o4-mini-2025-04-16"     # OpenAI model for code switching
  max_tokens: 100000              # Maximum output tokens (increased for long responses)
  temperature: 1.0                # Sampling temperature
  use_batch_api: true             # Use batch API for cost efficiency
  batch_size: 50                  # Reduced batch size for long inputs
  max_concurrent_requests: 5      # Reduced concurrent requests for stability
  requests_per_minute: 500        # Reduced rate limit for long processing
  max_retries: 5                  # Increased retries for long content
  timeout: 600                    # Increased timeout for long processing (10 minutes)
  batch_timeout_hours: 48         # Increased batch timeout for complex processing

  # Long content handling
  max_input_length: 200000        # Maximum input length in characters
  chunk_size: 150000              # Chunk size for extremely long inputs
  overlap_size: 5000              # Overlap between chunks to maintain context
  enable_chunking: true           # Enable automatic chunking for long inputs

  # Content quality validation
  min_length_ratio: 0.5           # Minimum acceptable output/input length ratio

# Logging Configuration
logging:
  level: "INFO"                   # Logging level
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: null                      # Optional log file path

# Default Paths (can be overridden by CLI arguments)
paths:
  data_dir: "./data"              # Default data directory
  output_dir: "./outputs"         # Default output directory
  system_prompt: "system_prompt.txt"  # System prompt file for code switching
