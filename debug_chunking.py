#!/usr/bin/env python3
"""
Debug script to analyze the chunking behavior and identify the short content issue
"""

import asyncio
from pathlib import Path
from kullm_pro.code_switching.openai_client import OpenAIClient, OpenAIConfig
from kullm_pro.code_switching.dataset_processor import DatasetProcessor

def analyze_chunking_behavior():
    """Analyze how content is being chunked"""

    # Create a mock long content similar to GAIR/LIMO
    sample_long_content = """
    To solve this problem, we need to find the derivative of the function f(x) = x^3 + 2x^2 - 5x + 3.

    First, let's recall the power rule for derivatives: if f(x) = x^n, then f'(x) = nx^(n-1).

    Applying this rule to each term:
    - The derivative of x^3 is 3x^2
    - The derivative of 2x^2 is 4x
    - The derivative of -5x is -5
    - The derivative of the constant 3 is 0

    Therefore, f'(x) = 3x^2 + 4x - 5.

    To verify this result, we can check by taking the derivative of our answer:
    If f'(x) = 3x^2 + 4x - 5, then f''(x) = 6x + 4.

    This matches what we would expect for the second derivative of the original function.

    Now, let's find the critical points by setting f'(x) = 0:
    3x^2 + 4x - 5 = 0

    Using the quadratic formula: x = (-b ± √(b^2 - 4ac)) / (2a)
    Where a = 3, b = 4, c = -5

    x = (-4 ± √(16 + 60)) / 6
    x = (-4 ± √76) / 6
    x = (-4 ± 2√19) / 6
    x = (-2 ± √19) / 3

    So the critical points are at x = (-2 + √19)/3 and x = (-2 - √19)/3.

    To determine whether these are maxima or minima, we can use the second derivative test:
    f''(x) = 6x + 4

    For x = (-2 + √19)/3:
    f''((-2 + √19)/3) = 6((-2 + √19)/3) + 4 = 2(-2 + √19) + 4 = -4 + 2√19 + 4 = 2√19 > 0

    Since f''(x) > 0, this critical point is a local minimum.

    For x = (-2 - √19)/3:
    f''((-2 - √19)/3) = 6((-2 - √19)/3) + 4 = 2(-2 - √19) + 4 = -4 - 2√19 + 4 = -2√19 < 0

    Since f''(x) < 0, this critical point is a local maximum.

    The function has a local maximum at x = (-2 - √19)/3 and a local minimum at x = (-2 + √19)/3.

    We can also analyze the behavior at the boundaries. As x approaches positive infinity, f(x) approaches positive infinity since the leading term x^3 dominates. As x approaches negative infinity, f(x) approaches negative infinity.

    Therefore, the complete analysis shows that the function increases from negative infinity, reaches a local maximum, decreases to a local minimum, and then increases to positive infinity.
    """ * 10  # Make it longer to trigger chunking

    print(f"Sample content length: {len(sample_long_content)} characters")

    # Test chunking logic
    config = OpenAIConfig(
        api_key="dummy",  # We won't make actual API calls
        max_input_length=5000,  # Set low to trigger chunking
        chunk_size=2000,
        overlap_size=200,
        enable_chunking=True
    )

    client = OpenAIClient(config)

    # Test the chunking function
    chunks = client._chunk_long_content(sample_long_content)

    print(f"\nChunking results:")
    print(f"Number of chunks: {len(chunks)}")

    for i, chunk in enumerate(chunks):
        print(f"\nChunk {i+1}:")
        print(f"Length: {len(chunk)} characters")
        print(f"First 100 chars: {chunk[:100]}...")
        print(f"Last 100 chars: ...{chunk[-100:]}")

    # Check overlap
    if len(chunks) > 1:
        for i in range(len(chunks) - 1):
            current_chunk = chunks[i]
            next_chunk = chunks[i + 1]

            # Find overlap
            overlap_found = False
            for j in range(min(len(current_chunk), config.overlap_size), 0, -1):
                if current_chunk[-j:] in next_chunk[:config.overlap_size * 2]:
                    print(f"\nOverlap between chunk {i+1} and {i+2}: {j} characters")
                    overlap_found = True
                    break

            if not overlap_found:
                print(f"\nNo overlap found between chunk {i+1} and {i+2}")

def analyze_system_prompt():
    """Analyze the system prompt to understand expected behavior"""

    with open("system_prompt.txt", "r", encoding="utf-8") as f:
        system_prompt = f.read()

    print(f"System prompt length: {len(system_prompt)} characters")
    print(f"System prompt word count: {len(system_prompt.split())}")

    # Look for key instructions about preserving content
    key_phrases = [
        "same exact logical flow",
        "number of sentences",
        "equations from the original content",
        "conceptual efficiency",
        "deep, concept-based code-switching"
    ]

    print("\nKey instructions found in system prompt:")
    for phrase in key_phrases:
        if phrase.lower() in system_prompt.lower():
            print(f"✓ Found: '{phrase}'")
        else:
            print(f"✗ Missing: '{phrase}'")

def test_dataset_loading():
    """Test loading a small sample from GAIR/LIMO to see typical content length"""

    try:
        processor = DatasetProcessor("./test_data")

        # Load just 2 samples to see typical content
        data = processor.load_and_filter_dataset(
            dataset_name="GAIR/LIMO",
            split="train",
            n_samples=2,
            text_column="solution"
        )

        print(f"\nLoaded {len(data)} samples from GAIR/LIMO")

        for i, item in enumerate(data):
            print(f"\nSample {i+1}:")
            print(f"Question length: {len(item.get('question', ''))} chars")
            print(f"Solution length: {len(item.get('solution', ''))} chars")
            print(f"Answer length: {len(item.get('answer', ''))} chars")

            # Show first part of solution
            solution = item.get('solution', '')
            print(f"Solution preview: {solution[:200]}...")

    except Exception as e:
        print(f"Error loading dataset: {e}")

if __name__ == "__main__":
    print("=== Analyzing Chunking Behavior ===")
    analyze_chunking_behavior()

    print("\n\n=== Analyzing System Prompt ===")
    analyze_system_prompt()

    print("\n\n=== Testing Dataset Loading ===")
    test_dataset_loading()
