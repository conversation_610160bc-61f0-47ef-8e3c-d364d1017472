# KULLM Pro Examples

This directory contains example configurations, sample data, and usage examples for KULLM Pro.

## 📁 Contents

### Configuration Files
- `config_small_model.yaml` - Configuration for testing with smaller models

### Sample Data
- `sample_training_data.jsonl` - Example training data with think tokens

## 🚀 Quick Start

### 1. Test Dataset Processing with Small Dataset

```bash
# Process LIMO dataset for reasoning training
python process_dataset.py run "GAIR/LIMO" \
  --split="train" \
  --n=5 \
  --config_file="examples/config_small_model.yaml" \
  --output_dir="examples/outputs"
```

### 2. Test Fine-tuning with Reasoning Data

```bash
# Train a reasoning model with think tokens
python fine_tune.py train \
  --train_file="examples/sample_training_data.jsonl" \
  --output_dir="examples/outputs/reasoning_model" \
  --config_file="examples/config_small_model.yaml" \
  --epochs=1 \
  --batch_size=1
```

### 3. Evaluate the Trained Model

```bash
# Evaluate the trained reasoning model
python fine_tune.py evaluate \
  --model_dir="examples/outputs/reasoning_model" \
  --eval_file="examples/sample_training_data.jsonl" \
  --config_file="examples/config_small_model.yaml"
```

## 📊 Expected Outputs

### Dataset Processing Output
- `examples/outputs/GAIR_LIMO_train_5_reasoning.jsonl` - Reasoning data with think tokens
- `examples/outputs/processing_stats.json` - Processing statistics

### Fine-tuning Output
- `examples/outputs/reasoning_model/` - Trained model directory
  - `adapter_config.json` - LoRA adapter configuration
  - `adapter_model.safetensors` - LoRA adapter weights
  - `tokenizer.json` - Tokenizer files
  - `training_info.json` - Training information and statistics

## 🔧 Customization

### Adjust Model Configuration
Edit `config_small_model.yaml` to change model settings:

```yaml
model:
  name: "your-preferred-model"  # Change model
  max_length: 1024              # Adjust context length

training:
  epochs: 3                     # More training epochs
  batch_size: 2                 # Larger batch size
```

### Create Custom Training Data
Follow the format in `sample_training_data.jsonl` with think tokens:

```json
{
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful assistant that solves mathematical problems step by step using structured reasoning. Use <think> and </think> tokens to show your reasoning process, then provide the final answer."
    },
    {
      "role": "user",
      "content": "Solve the following mathematical problem step by step:\n\nWhat is 2 + 2?"
    },
    {
      "role": "assistant",
      "content": "<think>\nI need to add 2 + 2.\nThis is a simple addition problem.\n2 + 2 = 4\n</think>\n\n4"
    }
  ]
}
```

## 🧪 Testing Different Scenarios

### Test with Different Models
```bash
# Test with different model sizes
python fine_tune.py train \
  --train_file="examples/sample_training_data.jsonl" \
  --output_dir="examples/outputs/gpt2_model" \
  --model_name="gpt2" \
  --epochs=1
```

### Test Dataset Validation
```bash
# Validate dataset format for reasoning training
python process_dataset.py validate "GAIR/LIMO"
```

### Test with Custom Parameters
```bash
# Test with custom LoRA parameters
python fine_tune.py train \
  --train_file="examples/sample_training_data.jsonl" \
  --output_dir="examples/outputs/custom_lora" \
  --lora_r=8 \
  --lora_alpha=16 \
  --learning_rate=0.001
```

## 📝 Notes

- The small model configuration is designed for testing and development
- Sample data contains only basic mathematical problems
- For production use, use the main `config.yaml` with larger models
- Adjust batch sizes and model parameters based on your hardware capabilities

## 🐛 Troubleshooting

### Common Issues

1. **Out of Memory Error**
   - Reduce `batch_size` to 1
   - Use smaller model (e.g., "gpt2")
   - Reduce `max_length`

2. **Dataset Loading Issues**
   - Check internet connection for dataset download
   - Verify dataset name and split are correct
   - Ensure dataset has required columns (question, solution, answer)

3. **Model Loading Issues**
   - Ensure you have internet connection for model download
   - Check if model name is correct
   - Try using `trust_remote_code=True` for custom models

### Getting Help

- Check the main README.md for detailed documentation
- Review the configuration files for parameter explanations
- Run commands with `--help` for usage information
- Check the logs for detailed error messages
