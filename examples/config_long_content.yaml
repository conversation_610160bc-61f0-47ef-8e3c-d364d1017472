# KULLM-Pro Configuration - Optimized for Extremely Long Content
# This configuration is specifically designed for processing very long system prompts
# and extremely long input data that requires code switching

# Model Configuration - Using larger context models
model:
  name: "Qwen/Qwen2.5-7B-Instruct"  # Model with large context window
  max_length: 32768                  # Increased for long content
  torch_dtype: "bfloat16"
  device_map: "auto"
  attn_implementation: "flash_attention_2"  # More efficient for long sequences

# Training Configuration - Optimized for long sequences
training:
  # Basic Training Parameters
  epochs: 3
  batch_size: 1                      # Reduced for memory efficiency with long content
  learning_rate: 0.0002
  gradient_accumulation_steps: 16    # Increased to maintain effective batch size
  weight_decay: 0.01
  warmup_ratio: 0.1
  lr_scheduler_type: "cosine"

  # Evaluation and Saving
  eval_steps: 50
  save_steps: 200
  logging_steps: 10
  save_total_limit: 3
  load_best_model_at_end: true
  metric_for_best_model: "eval_loss"

  # Performance Optimizations for Long Content
  bf16: true
  fp16: false
  gradient_checkpointing: true       # Essential for long sequences
  dataloader_num_workers: 2
  optim: "adamw_torch"
  max_grad_norm: 1.0                 # Gradient clipping for stability

# LoRA Configuration - Optimized for long content
lora:
  r: 32                              # Higher rank for complex long-form reasoning
  alpha: 64                          # Proportional alpha
  dropout: 0.05                      # Lower dropout for stability
  bias: "none"
  task_type: "CAUSAL_LM"
  target_modules:
    - "q_proj"
    - "k_proj"
    - "v_proj"
    - "o_proj"
    - "gate_proj"
    - "up_proj"
    - "down_proj"

# Experiment Tracking
wandb:
  project: "kullm-pro-long-content"
  enabled: true
  entity: null
  tags: ["long-content", "code-switching", "mathematical-reasoning"]

# Data Configuration
data:
  train_split_ratio: 0.9
  val_split_ratio: 0.1

# OpenAI Configuration - Optimized for Long Content Processing
openai:
  model: "o4-mini-2025-04-16"       # Model with large context window
  max_tokens: 100000                # Maximum output tokens for long responses
  temperature: 1.0                  # Maintain creativity for code switching
  use_batch_api: true               # Use batch API for cost efficiency
  batch_size: 25                    # Reduced batch size for long content
  max_concurrent_requests: 3        # Conservative concurrency for stability
  requests_per_minute: 300          # Reduced rate for long processing
  max_retries: 5                    # Increased retries for complex content
  timeout: 900                      # 15 minutes timeout for very long content
  batch_timeout_hours: 72           # Extended timeout for complex batches

  # Long Content Handling Configuration
  max_input_length: 150000          # 150K characters max per request
  chunk_size: 100000                # 100K character chunks
  overlap_size: 10000               # 10K character overlap for context
  enable_chunking: true             # Enable automatic chunking

  # Content quality validation
  min_length_ratio: 0.6             # Minimum acceptable output/input length ratio (stricter for long content)

  # Advanced chunking options
  smart_chunking: true              # Use intelligent sentence/paragraph boundaries
  preserve_context: true           # Maintain context across chunks
  chunk_merge_strategy: "concatenate"  # How to merge chunk results

# Logging Configuration - Enhanced for long content debugging
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/long_content_processing.log"  # Log to file for analysis

  # Additional logging for long content
  log_chunk_details: true          # Log chunking information
  log_token_usage: true            # Log detailed token usage
  log_processing_times: true       # Log processing time per chunk

# Default Paths
paths:
  data_dir: "./data"
  output_dir: "./outputs"
  system_prompt: "system_prompt.txt"  # Your long system prompt file

  # Additional paths for long content processing
  chunk_cache_dir: "./cache/chunks"   # Cache chunked content
  processing_logs_dir: "./logs"       # Processing logs directory

# Performance Optimization for Long Content
performance:
  # Memory management
  clear_cache_frequency: 10         # Clear cache every N items
  max_memory_usage_gb: 24           # Maximum memory usage

  # Processing optimization
  parallel_chunk_processing: false  # Process chunks sequentially for stability
  chunk_processing_delay: 1.0       # Delay between chunk processing (seconds)

  # Quality assurance
  validate_chunk_continuity: true   # Ensure chunks maintain logical flow
  max_chunk_retries: 3              # Retry failed chunks

# Content Quality Settings
quality:
  # Long content specific quality checks
  min_chunk_overlap_ratio: 0.1      # Minimum overlap as ratio of chunk size
  max_chunk_size_variance: 0.2      # Maximum variance in chunk sizes

  # Code switching quality for long content
  maintain_context_across_chunks: true     # Preserve code-switching context
  consistent_terminology: true            # Use consistent terms across chunks
  preserve_mathematical_notation: true    # Keep math notation consistent

# Error Handling for Long Content
error_handling:
  # Chunk-level error handling
  continue_on_chunk_failure: true   # Continue processing if some chunks fail
  min_successful_chunks_ratio: 0.8  # Minimum ratio of successful chunks

  # Recovery strategies
  retry_failed_chunks: true         # Retry failed chunks with different parameters
  fallback_to_smaller_chunks: true  # Use smaller chunks if large ones fail

  # Notification settings
  notify_on_chunk_failures: true    # Log chunk failures
  detailed_error_reporting: true    # Include detailed error information
