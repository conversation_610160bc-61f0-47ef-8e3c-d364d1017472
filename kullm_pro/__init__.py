"""
KULLM Pro: Korean Reasoning Language Model with Think Tokens

A production-ready framework for creating reasoning-enhanced language models using
structured thinking tokens. KULLM Pro learns to reason step-by-step using `<think>`
and `</think>` tokens, enabling transparent and improved mathematical problem-solving
capabilities.

This package provides two main components:

1. **Dataset Processing Module** (`dataset_processing`):
   - Process any Hugging Face dataset with reasoning tasks
   - Format data with think tokens for transparent reasoning
   - Support for LIMO and other mathematical reasoning datasets
   - Automatic data validation and quality checks

2. **Fine-tuning Module** (`fine_tuning`):
   - LoRA (Low-Rank Adaptation) fine-tuning for efficient training
   - Advanced training features with Accelerate/DeepSpeed support
   - Weights & Biases integration for experiment tracking
   - Checkpoint management and resumable training
   - Comprehensive evaluation and model information utilities

3. **Utilities Module** (`utils`):
   - Configuration management with YAML support
   - Centralized logging setup
   - Helper functions for file operations and validation

Example Usage:
    ```python
    from kullm_pro import DatasetProcessor, FineTuningPipeline
    from kullm_pro import load_config, setup_logging

    # Setup logging
    logger = setup_logging()

    # Load configuration
    config = load_config("config.yaml")

    # Create pipelines
    dataset_processor = DatasetProcessor(...)
    fine_tune_pipeline = FineTuningPipeline(config)
    ```

Requirements:
    - Python 3.8+
    - PyTorch 2.0+
    - Transformers 4.44+
    - CUDA-compatible GPU (recommended for fine-tuning)

License:
    Apache 2.0 License - see LICENSE file for details

Authors: <AUTHORS>

Version:
    1.0.0 - Initial release with think token reasoning and LoRA fine-tuning
"""

__version__ = "1.0.0"
__author__ = "KULLM Pro Development Team"
__email__ = "<EMAIL>"
__license__ = "Apache-2.0"
__description__ = "Korean Reasoning Language Model with Think Tokens"
__url__ = "https://github.com/junkim100/KULLM-Pro"

# Import main components with error handling
try:
    from .dataset_processing import DatasetProcessingPipeline, DatasetProcessor
except ImportError as e:
    import warnings
    warnings.warn(f"Could not import dataset processing components: {e}")
    DatasetProcessingPipeline = None
    DatasetProcessor = None

try:
    from .fine_tuning import FineTuningPipeline
except ImportError as e:
    import warnings
    warnings.warn(f"Could not import FineTuningPipeline: {e}")
    FineTuningPipeline = None

try:
    from .utils import load_config, setup_logging
except ImportError as e:
    import warnings
    warnings.warn(f"Could not import utilities: {e}")
    load_config = None
    setup_logging = None

# Define public API
__all__ = [
    # Main pipeline classes
    "DatasetProcessingPipeline",
    "DatasetProcessor",
    "FineTuningPipeline",

    # Utility functions
    "load_config",
    "setup_logging",

    # Package metadata
    "__version__",
    "__author__",
    "__license__",
    "__description__",
]
