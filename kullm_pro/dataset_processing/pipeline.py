"""
Main dataset processing pipeline for reasoning models
"""

import json
from datetime import datetime
from typing import List, Optional
from pathlib import Path

from .dataset_processor import DatasetProcessor
from ..utils.logging import get_logger
from ..utils.helpers import ensure_directory

logger = get_logger("dataset_processing.pipeline")


class DatasetProcessingPipeline:
    """Main pipeline for processing datasets for reasoning training"""

    def __init__(
        self,
        output_dir: str = "./data",
        think_start: str = "<think>",
        think_end: str = "</think>"
    ):
        self.dataset_processor = DatasetProcessor(output_dir, think_start, think_end)
        self.output_dir = Path(output_dir)
        ensure_directory(self.output_dir)

    def process_dataset(
        self,
        dataset_name: str,
        split: str = "train",
        subset: Optional[str] = None,
        n_samples: Optional[int] = None,
        question_column: str = "question",
        solution_column: str = "solution",
        answer_column: str = "answer",
        validate_quality: bool = True
    ) -> str:
        """
        Process a dataset for reasoning training with think tokens

        Args:
            dataset_name: Name of the Hugging Face dataset
            split: Dataset split to use
            subset: Dataset subset (if applicable)
            n_samples: Number of samples to process
            question_column: Column name for questions
            solution_column: Column name for solutions
            answer_column: Column name for answers
            validate_quality: Whether to validate data quality

        Returns:
            Path to saved reasoning dataset file
        """
        logger.info(f"Starting reasoning dataset processing for {dataset_name}")
        logger.info(f"Parameters: split={split}, subset={subset}, n_samples={n_samples}")

        # Process dataset using the dataset processor
        reasoning_file_path = self.dataset_processor.process_dataset_for_reasoning(
            dataset_name=dataset_name,
            split=split,
            subset=subset,
            n_samples=n_samples,
            question_column=question_column,
            solution_column=solution_column,
            answer_column=answer_column,
            validate_quality=validate_quality
        )

        # Save processing statistics
        self._save_processing_stats(dataset_name, split, subset, n_samples, reasoning_file_path)

        logger.info("=== Processing Complete ===")
        logger.info(f"Reasoning dataset: {reasoning_file_path}")

        return reasoning_file_path

    def _save_processing_stats(
        self,
        dataset_name: str,
        split: str,
        subset: Optional[str],
        n_samples: Optional[int],
        reasoning_file_path: str
    ):
        """Save processing statistics"""

        stats_data = {
            "timestamp": datetime.now().isoformat(),
            "dataset_info": {
                "name": dataset_name,
                "split": split,
                "subset": subset,
                "requested_samples": n_samples,
            },
            "processing_info": {
                "output_file": reasoning_file_path,
                "think_tokens": {
                    "start": self.dataset_processor.think_start,
                    "end": self.dataset_processor.think_end
                }
            },
        }

        stats_file = self.output_dir / "processing_stats.json"
        with open(stats_file, "w", encoding="utf-8") as f:
            json.dump(stats_data, f, indent=2, ensure_ascii=False)

        logger.info(f"Processing statistics saved to {stats_file}")

    def get_supported_datasets(self) -> List[str]:
        """Get list of supported datasets for reasoning training"""
        return [
            "GAIR/LIMO",
            "microsoft/orca-math-word-problems-200k",
            "deepmind/math_dataset",
            # Add more supported datasets here
        ]

    def validate_dataset_format(
        self,
        dataset_name: str,
        split: str = "train",
        subset: Optional[str] = None,
        question_column: str = "question",
        solution_column: str = "solution",
        answer_column: str = "answer"
    ) -> bool:
        """
        Validate that a dataset has the required format for reasoning training

        Args:
            dataset_name: Name of the dataset
            split: Dataset split
            subset: Dataset subset
            question_column: Expected question column name
            solution_column: Expected solution column name
            answer_column: Expected answer column name

        Returns:
            True if dataset format is valid, False otherwise
        """
        try:
            # Load a small sample to check format
            sample_data = self.dataset_processor.load_and_filter_dataset(
                dataset_name=dataset_name,
                split=split,
                subset=subset,
                n_samples=1,
                text_column=solution_column
            )

            if not sample_data:
                logger.error(f"Dataset {dataset_name} is empty")
                return False

            # Check required columns exist
            required_columns = [question_column, solution_column, answer_column]
            sample_item = sample_data[0]

            for col in required_columns:
                if col not in sample_item:
                    logger.error(f"Dataset {dataset_name} missing required column: {col}")
                    return False

            logger.info(f"Dataset {dataset_name} format validation passed")
            return True

        except Exception as e:
            logger.error(f"Dataset {dataset_name} format validation failed: {e}")
            return False
