#!/usr/bin/env python3
"""
KULLM Pro Dataset Processing CLI

Command-line interface for processing datasets for reasoning model training
with think tokens.

This script provides a user-friendly interface to:
- Load any Hugging Face dataset with reasoning tasks
- Format data with think tokens for transparent reasoning
- Save data in training-ready format for reasoning models
- Validate dataset quality and format
- Generate descriptive filenames automatically

Features:
- Support for mathematical reasoning datasets
- Automatic think token formatting
- Data quality validation
- Flexible dataset parameters (split, subset, sample count)
- Comprehensive error handling and logging
- Flexible output directory management

Example usage:
    # Basic dataset processing
    python process_dataset.py run "GAIR/LIMO" --split="train" --n=300

    # With custom parameters
    python process_dataset.py run "GAIR/LIMO" \\
        --split="train" --n=1000 --output_dir="./data"

    # Validate dataset format
    python process_dataset.py validate "GAIR/LIMO"

    # List supported datasets
    python process_dataset.py list_datasets

    # Get help
    python process_dataset.py --help
    python process_dataset.py run --help

Requirements:
    - Internet connection for dataset download
    - Sufficient disk space for dataset storage

Output:
    - {dataset_name}_{split}_{subset}_{n_samples}_reasoning.jsonl
    - processing_stats.json (processing statistics)

Author: KULLM Pro Development Team
License: Apache-2.0
"""

import sys
from typing import Optional

import fire

from kullm_pro.dataset_processing import DatasetProcessingPipeline
from kullm_pro.utils import setup_logging, load_config


class DatasetProcessingCLI:
    """
    Command-line interface for dataset processing operations.

    This class provides methods for processing datasets and formatting them
    for reasoning model training with think tokens.

    Attributes:
        logger: Configured logger instance for operation tracking

    Methods:
        run: Process a single dataset for reasoning training
        validate: Validate dataset format
        list_datasets: List supported datasets
    """

    def __init__(self):
        """
        Initialize the CLI with proper logging setup.

        Sets up logging configuration and handles any initialization errors
        gracefully by falling back to a basic logger.
        """
        try:
            self.logger = setup_logging()
        except Exception as e:
            print(f"Failed to setup logging: {e}")
            import logging
            self.logger = logging.getLogger(__name__)

    def run(
        self,
        dataset: str,
        split: str = "train",
        subset: Optional[str] = None,
        n: Optional[int] = None,
        output_dir: str = "./data",
        config_file: str = "config.yaml",
        question_column: str = "question",
        solution_column: str = "solution",
        answer_column: str = "answer",
        validate_quality: bool = True,
        think_start: str = "<think>",
        think_end: str = "</think>"
    ):
        """
        Process dataset for reasoning training with think tokens

        Args:
            dataset: Hugging Face dataset name (e.g., "GAIR/LIMO")
            split: Dataset split to use (default: "train")
            subset: Dataset subset if applicable (default: None)
            n: Number of samples to process (default: None for all)
            output_dir: Output directory for generated files (default: "./data")
            config_file: Path to configuration file (default: "config.yaml")
            question_column: Column name for questions (default: "question")
            solution_column: Column name for solutions (default: "solution")
            answer_column: Column name for answers (default: "answer")
            validate_quality: Whether to validate data quality (default: True)
            think_start: Start token for reasoning (default: "<think>")
            think_end: End token for reasoning (default: "</think>")
        """
        self.logger.info("Starting dataset processing pipeline")
        self.logger.info(f"Dataset: {dataset}")
        self.logger.info(f"Split: {split}")
        self.logger.info(f"Subset: {subset}")
        self.logger.info(f"Samples: {n}")

        # Load configuration
        try:
            config = load_config(config_file)
            self.logger.info(f"Loaded configuration from {config_file}")
        except FileNotFoundError:
            self.logger.warning(f"Configuration file not found: {config_file}")
            self.logger.warning("Using default configuration")
            config = {}
        except Exception as e:
            self.logger.warning(f"Could not load config file {config_file}: {e}")
            self.logger.warning("Using default configuration")
            config = {}

        # Create pipeline
        pipeline = DatasetProcessingPipeline(
            output_dir=output_dir,
            think_start=think_start,
            think_end=think_end
        )

        # Run the pipeline
        try:
            reasoning_file = pipeline.process_dataset(
                dataset_name=dataset,
                split=split,
                subset=subset,
                n_samples=n,
                question_column=question_column,
                solution_column=solution_column,
                answer_column=answer_column,
                validate_quality=validate_quality
            )

            self.logger.info("Dataset processing completed successfully!")
            self.logger.info(f"Reasoning dataset: {reasoning_file}")

            return {
                "reasoning_file": reasoning_file,
                "status": "success"
            }

        except Exception as e:
            self.logger.error(f"Dataset processing failed: {e}")
            raise

    def validate(
        self,
        dataset: str,
        split: str = "train",
        subset: Optional[str] = None,
        question_column: str = "question",
        solution_column: str = "solution",
        answer_column: str = "answer"
    ):
        """
        Validate dataset format for reasoning training

        Args:
            dataset: Hugging Face dataset name (e.g., "GAIR/LIMO")
            split: Dataset split to use (default: "train")
            subset: Dataset subset if applicable (default: None)
            question_column: Column name for questions (default: "question")
            solution_column: Column name for solutions (default: "solution")
            answer_column: Column name for answers (default: "answer")
        """
        self.logger.info(f"Validating dataset format: {dataset}")

        # Create pipeline
        pipeline = DatasetProcessingPipeline()

        # Validate dataset
        try:
            is_valid = pipeline.validate_dataset_format(
                dataset_name=dataset,
                split=split,
                subset=subset,
                question_column=question_column,
                solution_column=solution_column,
                answer_column=answer_column
            )

            if is_valid:
                self.logger.info(f"✅ Dataset {dataset} format is valid for reasoning training")
                return {"status": "valid", "dataset": dataset}
            else:
                self.logger.error(f"❌ Dataset {dataset} format is invalid")
                return {"status": "invalid", "dataset": dataset}

        except Exception as e:
            self.logger.error(f"Validation failed: {e}")
            return {"status": "error", "dataset": dataset, "error": str(e)}

    def list_datasets(self):
        """
        List supported datasets for reasoning training
        """
        self.logger.info("Listing supported datasets for reasoning training")

        # Create pipeline
        pipeline = DatasetProcessingPipeline()

        # Get supported datasets
        supported_datasets = pipeline.get_supported_datasets()

        self.logger.info("Supported datasets:")
        for dataset in supported_datasets:
            print(f"  - {dataset}")

        return {"supported_datasets": supported_datasets}


def main():
    """Main entry point"""
    fire.Fire(DatasetProcessingCLI())


if __name__ == "__main__":
    main()
