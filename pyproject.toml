[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "kullm-pro"
version = "1.0.0"
description = "Korean Reasoning Language Model with Think Tokens"
readme = "README.md"
license = {text = "Apache-2.0"}
authors = [
    {name = "KULLM-Pro Development Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "KULLM-Pro Development Team", email = "<EMAIL>"}
]
keywords = [
    "machine-learning",
    "nlp",
    "reasoning",
    "think-tokens",
    "fine-tuning",
    "korean",
    "language-model",
    "lora",
    "transformers",
    "mathematical-reasoning"
]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: Apache Software License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
requires-python = ">=3.8"
dependencies = [
    "torch>=2.0.0,<3.0.0",
    "transformers>=4.44.0,<4.46.0",
    "datasets>=2.14.0,<3.0.0",
    "accelerate>=0.34.0,<1.0.0",
    "peft>=0.7.0,<1.0.0",
    "bitsandbytes>=0.41.0,<1.0.0",
    "wandb>=0.16.0,<1.0.0",
    "numpy>=1.24.0,<2.0.0",
    "pandas>=2.0.0,<3.0.0",
    "scikit-learn>=1.3.0,<2.0.0",
    "tqdm>=4.65.0,<5.0.0",
    "openai>=1.0.0,<2.0.0",
    "tenacity>=8.2.0,<9.0.0",
    "aiohttp>=3.8.0,<4.0.0",
    "python-dotenv>=1.0.0,<2.0.0",
    "PyYAML>=6.0.0,<7.0.0",
    "fire>=0.5.0,<1.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0,<8.0.0",
    "pytest-cov>=4.0.0,<5.0.0",
    "pytest-asyncio>=0.21.0,<1.0.0",
    "black>=23.0.0,<24.0.0",
    "isort>=5.12.0,<6.0.0",
    "flake8>=6.0.0,<7.0.0",
    "mypy>=1.0.0,<2.0.0",
    "pre-commit>=3.0.0,<4.0.0",
    "bandit>=1.7.0,<2.0.0",
    "safety>=2.0.0,<3.0.0",
]
docs = [
    "sphinx>=5.0.0,<6.0.0",
    "sphinx-rtd-theme>=1.0.0,<2.0.0",
    "myst-parser>=0.18.0,<1.0.0",
]
viz = [
    "matplotlib>=3.7.0,<4.0.0",
    "seaborn>=0.12.0,<1.0.0",
]

[project.urls]
Homepage = "https://github.com/junkim100/KULLM-Pro"
Documentation = "https://github.com/junkim100/KULLM-Pro#readme"
Repository = "https://github.com/junkim100/KULLM-Pro.git"
"Bug Tracker" = "https://github.com/junkim100/KULLM-Pro/issues"
Changelog = "https://github.com/junkim100/KULLM-Pro/blob/main/CHANGELOG.md"

[project.scripts]
process-dataset = "process_dataset:main"
fine-tune = "fine_tune:main"

[tool.setuptools.packages.find]
include = ["kullm_pro*"]
exclude = ["tests*", "docs*", "examples*"]

[tool.setuptools.package-data]
kullm_pro = ["*.yaml", "*.yml", "*.txt"]

# Black configuration
[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort configuration
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["kullm_pro"]
known_third_party = [
    "torch",
    "transformers",
    "datasets",
    "accelerate",
    "peft",
    "wandb",
    "openai",
    "fire",
    "numpy",
    "pandas",
    "sklearn",
    "tqdm",
    "tenacity",
    "aiohttp",
    "yaml",
    "dotenv"
]

# MyPy configuration
[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "transformers.*",
    "datasets.*",
    "accelerate.*",
    "peft.*",
    "wandb.*",
    "fire.*",
    "tenacity.*",
    "aiohttp.*",
]
ignore_missing_imports = true

# Pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=kullm_pro",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
testpaths = ["tests"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "gpu: marks tests that require GPU",
    "api: marks tests that require API access",
]

# Coverage configuration
[tool.coverage.run]
source = ["kullm_pro"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/site-packages/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
